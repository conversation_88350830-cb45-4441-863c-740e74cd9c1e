# Trusty TEE GP存储API架构设计 - OP-TEE完整适配版

## 1. 引言

### 1.1 设计目标

本文档设计一套完整的GP存储API架构，完全基于OP-TEE成熟的双层对象模型（tee_obj + tee_pobj），并将存储后端完美适配Trusty存储服务接口，实现GP标准与Trusty TEE的无缝集成。

### 1.2 核心设计原则

1. **OP-TEE架构完全复制**：数据结构、管理方法、并发控制完全基于OP-TEE实现
2. **Trusty存储服务深度集成**：gp_storage_backend完全适配Trusty存储API
3. **GP标准严格遵循**：26个GP存储API完全符合GP TEE Internal Core API v1.3.1
4. **性能优化设计**：会话复用、路径缓存、批量操作优化

### 1.3 技术架构概览

```
┌─────────────────────────────────────────────────────────────┐
│                    GP Storage API Layer                     │
│              (26个GP标准API实现)                             │
├─────────────────────────────────────────────────────────────┤
│                OP-TEE Object Model Layer                    │
│         (gp_tee_obj + gp_pobj双层对象模型)                   │
├─────────────────────────────────────────────────────────────┤
│              GP Storage Backend Interface                   │
│           (gp_storage_backend适配层)                        │
├─────────────────────────────────────────────────────────────┤
│                Trusty Storage Service                       │
│        (storage_session_t + file_handle_t)                  │
└─────────────────────────────────────────────────────────────┘
```

## 2. OP-TEE对象模型完整适配

### 2.1 GP TEE对象结构（基于OP-TEE tee_obj）

```c
/* GP TEE对象结构 - 完全基于OP-TEE tee_obj设计 */
struct gp_tee_obj {
    /* 链表管理 - 对应OP-TEE的TAILQ_ENTRY */
    struct list_node link;         /* TA私有对象链表节点 */
    
    /* GP标准对象信息 - 完全兼容OP-TEE */
    TEE_ObjectInfo info;           /* GP标准对象信息 */
    
    /* 并发控制 - 完全保持OP-TEE设计 */
    bool busy;                     /* 操作忙标志，防止并发操作 */
    
    /* 属性管理 - 完全保持OP-TEE设计 */
    uint32_t have_attrs;           /* 属性位字段，标识已设置的属性 */
    void *attr;                    /* 属性数据指针 */
    
    /* 数据流管理 - 完全保持OP-TEE设计 */
    size_t ds_pos;                 /* 数据流起始位置偏移 */
    
    /* 存储抽象 - 适配Trusty机制 */
    struct gp_pobj *pobj;          /* 指向持久化对象的指针 */
    file_handle_t fh;              /* Trusty文件句柄 */
    
    /* Trusty特有扩展 */
    uint32_t handle_id;            /* 句柄唯一ID */
    mutex_t obj_lock;              /* 对象锁 */
};

/* GP持久对象结构 - 完全基于OP-TEE tee_pobj设计 */
struct gp_pobj {
    /* 链表管理 - 对应OP-TEE的TAILQ_ENTRY */
    struct list_node link;         /* 全局持久对象链表节点 */
    
    /* 引用计数 - 完全保持OP-TEE设计 */
    uint32_t refcnt;               /* 引用计数 */
    
    /* TA标识 - 完全保持OP-TEE设计 */
    struct uuid uuid;              /* TA UUID */
    void *obj_id;                  /* 对象ID */
    uint32_t obj_id_len;           /* 对象ID长度 */
    
    /* 访问控制 - 完全保持OP-TEE设计 */
    uint32_t flags;                /* 访问标志 */
    uint32_t obj_info_usage;       /* 使用权限 */
    
    /* 状态管理 - 完全保持OP-TEE设计 */
    bool temporary;                /* 创建过程中可修改 */
    bool creating;                 /* 正在创建状态 */
    
    /* 存储后端 - 适配Trusty机制 */
    struct gp_storage_backend *backend;  /* 存储后端接口 */
    char storage_path[GP_MAX_PATH_LEN];  /* 存储路径 */
    
    /* 并发控制 */
    mutex_t pobj_lock;             /* 持久对象锁 */
};
```

### 2.2 OP-TEE对象管理方法完整适配

```c
/* 对象分配 - 对应OP-TEE的tee_obj_alloc */
struct gp_tee_obj *gp_obj_alloc(void);

/* 对象释放 - 对应OP-TEE的tee_obj_free */
void gp_obj_free(struct gp_tee_obj *obj);

/* 对象添加到TA - 对应OP-TEE的tee_obj_add */
void gp_obj_add(struct gp_tee_obj *obj);

/* 对象获取 - 对应OP-TEE的tee_obj_get */
TEE_Result gp_obj_get(TEE_ObjectHandle object, struct gp_tee_obj **obj);

/* 对象关闭 - 对应OP-TEE的tee_obj_close */
void gp_obj_close(struct gp_tee_obj *obj);

/* 关闭所有对象 - 对应OP-TEE的tee_obj_close_all */
void gp_obj_close_all(void);

/* 对象验证 - 对应OP-TEE的tee_obj_verify */
TEE_Result gp_obj_verify(struct gp_tee_obj *obj);

/* 持久对象获取 - 对应OP-TEE的tee_pobj_get */
TEE_Result gp_pobj_get(struct uuid *uuid, void *obj_id, uint32_t obj_id_len,
                       uint32_t flags, enum gp_pobj_usage usage,
                       struct gp_storage_backend *backend, struct gp_pobj **obj);

/* 持久对象创建完成 - 对应OP-TEE的tee_pobj_create_final */
void gp_pobj_create_final(struct gp_pobj *obj);

/* 持久对象释放 - 对应OP-TEE的tee_pobj_release */
TEE_Result gp_pobj_release(struct gp_pobj *obj);

/* 持久对象重命名 - 对应OP-TEE的tee_pobj_rename */
TEE_Result gp_pobj_rename(struct gp_pobj *obj, void *obj_id, uint32_t obj_id_len);

/* 持久对象使用权限锁定 - 对应OP-TEE的tee_pobj_lock_usage */
void gp_pobj_lock_usage(struct gp_pobj *obj);

/* 持久对象使用权限解锁 - 对应OP-TEE的tee_pobj_unlock_usage */
void gp_pobj_unlock_usage(struct gp_pobj *obj);
```

### 2.3 OP-TEE并发控制机制适配

```c
/* 持久对象使用类型 - 对应OP-TEE的tee_pobj_usage */
enum gp_pobj_usage {
    GP_POBJ_USAGE_OPEN,
    GP_POBJ_USAGE_RENAME,
    GP_POBJ_USAGE_CREATE,
    GP_POBJ_USAGE_ENUM,
};

/* 对象忙状态管理 - 完全基于OP-TEE逻辑 */
TEE_Result gp_obj_set_busy(struct gp_tee_obj *obj) {
    mutex_acquire(&obj->obj_lock);
    
    if (obj->busy) {
        mutex_release(&obj->obj_lock);
        return TEE_ERROR_BUSY;
    }
    
    obj->busy = true;
    mutex_release(&obj->obj_lock);
    return TEE_SUCCESS;
}

/* 清除对象忙状态 - 完全基于OP-TEE逻辑 */
void gp_obj_clear_busy(struct gp_tee_obj *obj) {
    mutex_acquire(&obj->obj_lock);
    obj->busy = false;
    mutex_release(&obj->obj_lock);
}

/* 持久对象引用计数增加 */
static void gp_pobj_get_ref(struct gp_pobj *pobj) {
    mutex_acquire(&pobj->pobj_lock);
    pobj->refcnt++;
    mutex_release(&pobj->pobj_lock);
}

/* 持久对象引用计数减少 */
static void gp_pobj_put_ref(struct gp_pobj *pobj) {
    bool should_free = false;
    
    mutex_acquire(&pobj->pobj_lock);
    if (--pobj->refcnt == 0) {
        should_free = true;
    }
    mutex_release(&pobj->pobj_lock);
    
    if (should_free) {
        gp_pobj_free(pobj);
    }
}
```

## 3. GP存储后端接口 - Trusty存储服务完整适配

### 3.1 存储后端结构设计

```c
/* GP存储后端接口 - 完全适配Trusty存储服务 */
struct gp_storage_backend {
    /* 会话管理 */
    storage_session_t session;        /* Trusty存储会话 */
    const char *port_name;            /* 存储服务端口名 */
    bool session_active;              /* 会话状态标志 */
    uint32_t storage_type;            /* 存储类型 */
    
    /* 基础文件操作 - 对应OP-TEE的tee_file_operations */
    TEE_Result (*open)(struct gp_pobj *pobj, size_t *size, file_handle_t *fh);
    TEE_Result (*create)(struct gp_pobj *pobj, bool overwrite,
                        const void *head, size_t head_size,
                        const void *attr, size_t attr_size,
                        file_handle_t *fh);
    TEE_Result (*close)(file_handle_t *fh);
    TEE_Result (*read)(file_handle_t fh, size_t pos, void *buf, size_t *len);
    TEE_Result (*write)(file_handle_t fh, size_t pos, const void *buf, size_t len);
    TEE_Result (*truncate)(file_handle_t fh, size_t len);
    TEE_Result (*remove)(struct gp_pobj *pobj);
    TEE_Result (*rename)(struct gp_pobj *old_pobj, struct gp_pobj *new_pobj, bool overwrite);
    
    /* 枚举操作 - 对应OP-TEE的目录操作 */
    TEE_Result (*opendir)(uint32_t storage_id, void **dir_handle);
    TEE_Result (*readdir)(void *dir_handle, struct gp_pobj **pobj);
    TEE_Result (*closedir)(void *dir_handle);
    
    /* 文件信息查询 */
    TEE_Result (*get_file_size)(file_handle_t fh, size_t *size);
    TEE_Result (*file_exists)(const char *path, bool *exists);
    
    /* 并发控制 */
    mutex_t backend_lock;             /* 后端锁 */
};
```

### 3.2 Trusty存储服务适配实现

```c
/* 存储后端初始化 - 适配Trusty存储服务 */
TEE_Result gp_storage_backend_init(struct gp_storage_backend *backend, 
                                  uint32_t storage_type) {
    const char *port_name;
    int ret;
    
    if (!backend)
        return TEE_ERROR_BAD_PARAMETERS;
    
    /* 根据存储类型选择Trusty存储端口 */
    switch (storage_type) {
        case TEE_STORAGE_PRIVATE:
            port_name = STORAGE_CLIENT_TD_PORT;
            break;
        case TEE_STORAGE_PERSO:
            port_name = STORAGE_CLIENT_TDP_PORT;
            break;
        case TEE_STORAGE_PROTECTED:
            port_name = STORAGE_CLIENT_TP_PORT;
            break;
        default:
            return TEE_ERROR_BAD_PARAMETERS;
    }
    
    /* 打开Trusty存储会话 */
    ret = storage_open_session(&backend->session, port_name);
    if (ret != NO_ERROR)
        return gp_convert_storage_error(ret);
    
    backend->port_name = port_name;
    backend->session_active = true;
    backend->storage_type = storage_type;
    mutex_init(&backend->backend_lock);
    
    /* 设置函数指针 - 适配Trusty存储API */
    backend->open = gp_storage_open_impl;
    backend->create = gp_storage_create_impl;
    backend->close = gp_storage_close_impl;
    backend->read = gp_storage_read_impl;
    backend->write = gp_storage_write_impl;
    backend->truncate = gp_storage_truncate_impl;
    backend->remove = gp_storage_remove_impl;
    backend->rename = gp_storage_rename_impl;
    backend->opendir = gp_storage_opendir_impl;
    backend->readdir = gp_storage_readdir_impl;
    backend->closedir = gp_storage_closedir_impl;
    backend->get_file_size = gp_storage_get_file_size_impl;
    backend->file_exists = gp_storage_file_exists_impl;
    
    return TEE_SUCCESS;
}

/* 存储后端销毁 */
void gp_storage_backend_destroy(struct gp_storage_backend *backend) {
    if (!backend)
        return;
    
    if (backend->session_active) {
        storage_close_session(backend->session);
        backend->session_active = false;
    }
    
    mutex_destroy(&backend->backend_lock);
}
```

### 3.3 Trusty存储操作实现

```c
/* 打开文件实现 - 适配Trusty storage_open_file */
static TEE_Result gp_storage_open_impl(struct gp_pobj *pobj, size_t *size,
                                       file_handle_t *fh) {
    int ret;
    storage_off_t file_size;

    if (!pobj || !fh || !pobj->backend)
        return TEE_ERROR_BAD_PARAMETERS;

    /* 使用Trusty存储API打开文件 */
    ret = storage_open_file(pobj->backend->session, fh, pobj->storage_path,
                           STORAGE_FILE_OPEN_READ_WRITE, 0);
    if (ret != NO_ERROR)
        return gp_convert_storage_error(ret);

    /* 获取文件大小 */
    if (size) {
        ret = storage_get_file_size(*fh, &file_size);
        if (ret == NO_ERROR) {
            *size = (size_t)file_size;
        } else {
            *size = 0;
        }
    }

    return TEE_SUCCESS;
}

/* 创建文件实现 - 适配Trusty存储API */
static TEE_Result gp_storage_create_impl(struct gp_pobj *pobj, bool overwrite,
                                         const void *head, size_t head_size,
                                         const void *attr, size_t attr_size,
                                         file_handle_t *fh) {
    int ret;
    uint32_t flags = STORAGE_FILE_OPEN_CREATE | STORAGE_FILE_OPEN_READ_WRITE;

    if (!pobj || !fh || !pobj->backend)
        return TEE_ERROR_BAD_PARAMETERS;

    /* 设置覆盖标志 */
    if (!overwrite)
        flags |= STORAGE_FILE_OPEN_CREATE_EXCLUSIVE;

    /* 使用Trusty存储API创建文件 */
    ret = storage_open_file(pobj->backend->session, fh, pobj->storage_path, flags, 0);
    if (ret != NO_ERROR)
        return gp_convert_storage_error(ret);

    /* 写入头部数据 */
    if (head && head_size > 0) {
        ssize_t written = storage_write(*fh, 0, head, head_size, 0);
        if (written < 0 || (size_t)written != head_size) {
            storage_close_file(*fh);
            return TEE_ERROR_STORAGE_NOT_AVAILABLE;
        }
    }

    /* 写入属性数据 */
    if (attr && attr_size > 0) {
        ssize_t written = storage_write(*fh, head_size, attr, attr_size, 0);
        if (written < 0 || (size_t)written != attr_size) {
            storage_close_file(*fh);
            return TEE_ERROR_STORAGE_NOT_AVAILABLE;
        }
    }

    return TEE_SUCCESS;
}

/* 关闭文件实现 - 适配Trusty storage_close_file */
static TEE_Result gp_storage_close_impl(file_handle_t *fh) {
    if (!fh)
        return TEE_ERROR_BAD_PARAMETERS;

    storage_close_file(*fh);
    *fh = INVALID_IPC_HANDLE;
    return TEE_SUCCESS;
}

/* 读取文件实现 - 适配Trusty storage_read */
static TEE_Result gp_storage_read_impl(file_handle_t fh, size_t pos,
                                      void *buf, size_t *len) {
    ssize_t ret;

    if (!buf || !len)
        return TEE_ERROR_BAD_PARAMETERS;

    ret = storage_read(fh, pos, buf, *len);
    if (ret < 0)
        return gp_convert_storage_error((int)ret);

    *len = (size_t)ret;
    return TEE_SUCCESS;
}

/* 写入文件实现 - 适配Trusty storage_write */
static TEE_Result gp_storage_write_impl(file_handle_t fh, size_t pos,
                                       const void *buf, size_t len) {
    ssize_t ret;

    if (!buf && len > 0)
        return TEE_ERROR_BAD_PARAMETERS;

    ret = storage_write(fh, pos, buf, len, 0);
    if (ret < 0)
        return gp_convert_storage_error((int)ret);

    if ((size_t)ret != len)
        return TEE_ERROR_STORAGE_NOT_AVAILABLE;

    return TEE_SUCCESS;
}

/* 截断文件实现 - 适配Trusty storage_set_file_size */
static TEE_Result gp_storage_truncate_impl(file_handle_t fh, size_t len) {
    int ret;

    ret = storage_set_file_size(fh, len, 0);
    if (ret != NO_ERROR)
        return gp_convert_storage_error(ret);

    return TEE_SUCCESS;
}

/* 删除文件实现 - 适配Trusty storage_delete_file */
static TEE_Result gp_storage_remove_impl(struct gp_pobj *pobj) {
    int ret;

    if (!pobj || !pobj->backend)
        return TEE_ERROR_BAD_PARAMETERS;

    ret = storage_delete_file(pobj->backend->session, pobj->storage_path, 0);
    if (ret != NO_ERROR && ret != ERR_NOT_FOUND)
        return gp_convert_storage_error(ret);

    return TEE_SUCCESS;
}

/* 重命名文件实现 - 适配Trusty storage_move_file */
static TEE_Result gp_storage_rename_impl(struct gp_pobj *old_pobj,
                                         struct gp_pobj *new_pobj, bool overwrite) {
    int ret;
    uint32_t flags = overwrite ? STORAGE_FILE_MOVE_CREATE : 0;

    if (!old_pobj || !new_pobj || !old_pobj->backend)
        return TEE_ERROR_BAD_PARAMETERS;

    ret = storage_move_file(old_pobj->backend->session,
                           old_pobj->storage_path, new_pobj->storage_path, flags);
    if (ret != NO_ERROR)
        return gp_convert_storage_error(ret);

    return TEE_SUCCESS;
}
```

## 4. 目录枚举操作适配

### 4.1 目录枚举状态结构

```c
/* 目录枚举状态 - 适配Trusty存储目录操作 */
struct gp_dir_enum_state {
    storage_session_t session;           /* Trusty存储会话 */
    struct storage_open_dir_state state; /* Trusty目录枚举状态 */
    char prefix[GP_MAX_PATH_LEN];        /* 枚举路径前缀 */
    uint32_t storage_type;               /* 存储类型 */
    struct uuid ta_uuid;                 /* TA UUID */
    bool active;                         /* 枚举活跃状态 */
};
```

### 4.2 目录枚举操作实现

```c
/* 打开目录枚举 - 适配Trusty storage_open_dir */
static TEE_Result gp_storage_opendir_impl(uint32_t storage_id, void **dir_handle) {
    struct gp_dir_enum_state *enum_state;
    struct gp_storage_backend *backend;
    struct uuid ta_uuid;
    char prefix[GP_MAX_PATH_LEN];
    int ret;

    if (!dir_handle)
        return TEE_ERROR_BAD_PARAMETERS;

    /* 获取存储后端 */
    backend = gp_get_storage_backend(storage_id);
    if (!backend)
        return TEE_ERROR_BAD_PARAMETERS;

    /* 获取当前TA UUID */
    get_current_ta_uuid(&ta_uuid);

    /* 构建枚举前缀路径 */
    ret = gp_build_ta_base_path(&ta_uuid, storage_id, prefix, sizeof(prefix));
    if (ret != NO_ERROR)
        return gp_convert_storage_error(ret);

    /* 分配枚举状态 */
    enum_state = calloc(1, sizeof(*enum_state));
    if (!enum_state)
        return TEE_ERROR_OUT_OF_MEMORY;

    /* 初始化枚举状态 */
    enum_state->session = backend->session;
    enum_state->storage_type = storage_id;
    enum_state->ta_uuid = ta_uuid;
    strncpy(enum_state->prefix, prefix, sizeof(enum_state->prefix) - 1);
    enum_state->active = true;

    /* 开始Trusty目录枚举 */
    ret = storage_open_dir(backend->session, prefix, &enum_state->state);
    if (ret != NO_ERROR) {
        free(enum_state);
        return gp_convert_storage_error(ret);
    }

    *dir_handle = enum_state;
    return TEE_SUCCESS;
}

/* 读取目录项 - 适配Trusty storage_read_dir */
static TEE_Result gp_storage_readdir_impl(void *dir_handle, struct gp_pobj **pobj) {
    struct gp_dir_enum_state *enum_state = (struct gp_dir_enum_state *)dir_handle;
    char filename[GP_MAX_FILENAME_LEN];
    uint8_t flags;
    int ret;

    if (!enum_state || !pobj)
        return TEE_ERROR_BAD_PARAMETERS;

    if (!enum_state->active) {
        *pobj = NULL;
        return TEE_ERROR_ITEM_NOT_FOUND;
    }

    /* 读取下一个目录项 */
    ret = storage_read_dir(enum_state->session, &enum_state->state,
                          &flags, filename, sizeof(filename));
    if (ret == ERR_NOT_FOUND) {
        *pobj = NULL;
        return TEE_ERROR_ITEM_NOT_FOUND;
    } else if (ret != NO_ERROR) {
        enum_state->active = false;
        return gp_convert_storage_error(ret);
    }

    /* 解析文件名并创建pobj */
    ret = gp_parse_filename_to_pobj(filename, &enum_state->ta_uuid,
                                   enum_state->storage_type, pobj);
    if (ret != TEE_SUCCESS) {
        /* 跳过无效文件，继续枚举 */
        return gp_storage_readdir_impl(dir_handle, pobj);
    }

    return TEE_SUCCESS;
}

/* 关闭目录枚举 - 适配Trusty storage_close_dir */
static TEE_Result gp_storage_closedir_impl(void *dir_handle) {
    struct gp_dir_enum_state *enum_state = (struct gp_dir_enum_state *)dir_handle;

    if (!enum_state)
        return TEE_ERROR_BAD_PARAMETERS;

    /* 关闭Trusty目录枚举 */
    if (enum_state->active) {
        storage_close_dir(enum_state->session, &enum_state->state);
        enum_state->active = false;
    }

    free(enum_state);
    return TEE_SUCCESS;
}
```

## 5. 错误码映射和路径管理

### 5.1 完整错误码映射

```c
/* Trusty存储错误码到GP错误码的完整映射 */
TEE_Result gp_convert_storage_error(int storage_err) {
    switch (storage_err) {
        case NO_ERROR:
            return TEE_SUCCESS;
        case ERR_NOT_FOUND:
            return TEE_ERROR_ITEM_NOT_FOUND;
        case ERR_ACCESS_DENIED:
            return TEE_ERROR_ACCESS_DENIED;
        case ERR_ALREADY_EXISTS:
            return TEE_ERROR_ACCESS_CONFLICT;
        case ERR_NO_MEMORY:
            return TEE_ERROR_OUT_OF_MEMORY;
        case ERR_IO:
            return TEE_ERROR_STORAGE_NOT_AVAILABLE;
        case ERR_INVALID_ARGS:
            return TEE_ERROR_BAD_PARAMETERS;
        case ERR_NOT_ALLOWED:
            return TEE_ERROR_ACCESS_DENIED;
        case ERR_BUSY:
            return TEE_ERROR_BUSY;
        case ERR_GENERIC:
            return TEE_ERROR_GENERIC;
        case STORAGE_ERR_NOT_VALID:
            return TEE_ERROR_BAD_PARAMETERS;
        case STORAGE_ERR_UNIMPLEMENTED:
            return TEE_ERROR_NOT_SUPPORTED;
        case STORAGE_ERR_ACCESS:
            return TEE_ERROR_ACCESS_DENIED;
        case STORAGE_ERR_EXIST:
            return TEE_ERROR_ACCESS_CONFLICT;
        case STORAGE_ERR_TRANSACT:
            return TEE_ERROR_BAD_STATE;
        case STORAGE_ERR_SYNC_FAILURE:
            return TEE_ERROR_STORAGE_NOT_AVAILABLE;
        case STORAGE_ERR_CORRUPTED:
            return TEE_ERROR_CORRUPT_OBJECT;
        case STORAGE_ERR_FS_REPAIRED:
            return TEE_ERROR_STORAGE_NOT_AVAILABLE;
        default:
            return TEE_ERROR_GENERIC;
    }
}
```

### 5.2 路径管理和TA隔离

```c
/* 构建TA基础路径 */
int gp_build_ta_base_path(const struct uuid *ta_uuid, uint32_t storage_type,
                         char *path, size_t path_size) {
    const char *storage_prefix;
    char uuid_str[37];

    /* 根据存储类型选择前缀 */
    switch (storage_type) {
        case TEE_STORAGE_PRIVATE:
            storage_prefix = "private";
            break;
        case TEE_STORAGE_PERSO:
            storage_prefix = "perso";
            break;
        case TEE_STORAGE_PROTECTED:
            storage_prefix = "protected";
            break;
        default:
            return -EINVAL;
    }

    /* 格式化UUID字符串 */
    gp_uuid_to_string(ta_uuid, uuid_str, sizeof(uuid_str));

    /* 构建基础路径：storage_type/ta_uuid/ */
    int ret = snprintf(path, path_size, "%s/%s/", storage_prefix, uuid_str);

    if (ret >= (int)path_size)
        return -ENAMETOOLONG;

    return NO_ERROR;
}

/* 构建完整对象路径 */
int gp_build_object_path(const struct uuid *ta_uuid, const void *obj_id,
                        uint32_t obj_id_len, uint32_t storage_type,
                        char *path, size_t path_size) {
    char base_path[GP_MAX_PATH_LEN];
    char obj_id_hex[GP_MAX_OBJECT_ID_LEN * 2 + 1];
    int ret;

    /* 构建基础路径 */
    ret = gp_build_ta_base_path(ta_uuid, storage_type, base_path, sizeof(base_path));
    if (ret != NO_ERROR)
        return ret;

    /* 转换对象ID为十六进制字符串 */
    gp_obj_id_to_hex(obj_id, obj_id_len, obj_id_hex, sizeof(obj_id_hex));

    /* 构建完整路径：base_path + obj_id_hex */
    ret = snprintf(path, path_size, "%s%s", base_path, obj_id_hex);

    if (ret >= (int)path_size)
        return -ENAMETOOLONG;

    return NO_ERROR;
}
```

## 6. GP存储API实现框架

### 6.1 Generic Object Functions实现

```c
/* TEE_GetObjectInfo1 - 获取对象信息 */
void TEE_GetObjectInfo1(TEE_ObjectHandle object, TEE_ObjectInfo *objectInfo) {
    struct gp_tee_obj *obj;

    if (!objectInfo)
        return;

    if (gp_obj_get(object, &obj) != TEE_SUCCESS)
        return;

    *objectInfo = obj->info;
}

/* TEE_RestrictObjectUsage1 - 限制对象使用权限 */
TEE_Result TEE_RestrictObjectUsage1(TEE_ObjectHandle object, uint32_t objectUsage) {
    struct gp_tee_obj *obj;
    TEE_Result res;

    res = gp_obj_get(object, &obj);
    if (res != TEE_SUCCESS)
        return res;

    res = gp_obj_set_busy(obj);
    if (res != TEE_SUCCESS)
        return res;

    /* 只能收紧权限，不能扩展 */
    if ((objectUsage & ~obj->info.objectUsage) != 0) {
        gp_obj_clear_busy(obj);
        return TEE_ERROR_BAD_PARAMETERS;
    }

    obj->info.objectUsage &= objectUsage;
    gp_obj_clear_busy(obj);
    return TEE_SUCCESS;
}

/* TEE_CloseObject - 关闭对象 */
void TEE_CloseObject(TEE_ObjectHandle object) {
    struct gp_tee_obj *obj;

    if (gp_obj_get(object, &obj) != TEE_SUCCESS)
        return;

    gp_obj_close(obj);
}
```

### 6.2 Persistent Object Functions实现

```c
/* TEE_OpenPersistentObject - 打开持久对象 */
TEE_Result TEE_OpenPersistentObject(uint32_t storageID, const void *objectID,
                                   uint32_t objectIDLen, uint32_t flags,
                                   TEE_ObjectHandle *object) {
    struct gp_tee_obj *obj;
    struct gp_pobj *pobj;
    struct gp_storage_backend *backend;
    struct uuid ta_uuid;
    TEE_Result res;
    size_t file_size;

    if (!object || !objectID)
        return TEE_ERROR_BAD_PARAMETERS;

    /* 获取存储后端 */
    backend = gp_get_storage_backend(storageID);
    if (!backend)
        return TEE_ERROR_BAD_PARAMETERS;

    /* 获取当前TA UUID */
    get_current_ta_uuid(&ta_uuid);

    /* 获取或创建持久对象 */
    res = gp_pobj_get(&ta_uuid, (void *)objectID, objectIDLen, flags,
                      GP_POBJ_USAGE_OPEN, backend, &pobj);
    if (res != TEE_SUCCESS)
        return res;

    /* 分配对象句柄 */
    obj = gp_obj_alloc();
    if (!obj) {
        gp_pobj_release(pobj);
        return TEE_ERROR_OUT_OF_MEMORY;
    }

    /* 打开文件 */
    res = backend->open(pobj, &file_size, &obj->fh);
    if (res != TEE_SUCCESS) {
        gp_obj_free(obj);
        gp_pobj_release(pobj);
        return res;
    }

    /* 初始化对象信息 */
    obj->pobj = pobj;
    obj->info.handleFlags = TEE_HANDLE_FLAG_PERSISTENT;
    obj->info.dataSize = file_size;
    obj->info.dataPosition = 0;
    obj->ds_pos = 0;

    /* 读取对象头部信息 */
    res = gp_load_object_header(obj);
    if (res != TEE_SUCCESS) {
        backend->close(&obj->fh);
        gp_obj_free(obj);
        gp_pobj_release(pobj);
        return res;
    }

    gp_obj_add(obj);
    *object = (TEE_ObjectHandle)obj->handle_id;
    return TEE_SUCCESS;
}

/* TEE_CreatePersistentObject - 创建持久对象 */
TEE_Result TEE_CreatePersistentObject(uint32_t storageID, const void *objectID,
                                     uint32_t objectIDLen, uint32_t flags,
                                     TEE_ObjectHandle attributes,
                                     const void *initialData, uint32_t initialDataLen,
                                     TEE_ObjectHandle *object) {
    struct gp_tee_obj *obj, *attr_obj = NULL;
    struct gp_pobj *pobj;
    struct gp_storage_backend *backend;
    struct uuid ta_uuid;
    TEE_Result res;

    if (!object || !objectID)
        return TEE_ERROR_BAD_PARAMETERS;

    /* 获取属性对象（如果提供） */
    if (attributes != TEE_HANDLE_NULL) {
        res = gp_obj_get(attributes, &attr_obj);
        if (res != TEE_SUCCESS)
            return res;
    }

    /* 获取存储后端 */
    backend = gp_get_storage_backend(storageID);
    if (!backend)
        return TEE_ERROR_BAD_PARAMETERS;

    /* 获取当前TA UUID */
    get_current_ta_uuid(&ta_uuid);

    /* 创建持久对象 */
    res = gp_pobj_get(&ta_uuid, (void *)objectID, objectIDLen, flags,
                      GP_POBJ_USAGE_CREATE, backend, &pobj);
    if (res != TEE_SUCCESS)
        return res;

    pobj->creating = true;

    /* 分配对象句柄 */
    obj = gp_obj_alloc();
    if (!obj) {
        gp_pobj_release(pobj);
        return TEE_ERROR_OUT_OF_MEMORY;
    }

    /* 准备对象头部和属性数据 */
    void *header_data = NULL;
    size_t header_size = 0;
    void *attr_data = NULL;
    size_t attr_size = 0;

    if (attr_obj) {
        res = gp_serialize_object_attributes(attr_obj, &attr_data, &attr_size);
        if (res != TEE_SUCCESS) {
            gp_obj_free(obj);
            gp_pobj_release(pobj);
            return res;
        }
        obj->info = attr_obj->info;
    }

    res = gp_create_object_header(obj, &header_data, &header_size);
    if (res != TEE_SUCCESS) {
        if (attr_data) free(attr_data);
        gp_obj_free(obj);
        gp_pobj_release(pobj);
        return res;
    }

    /* 创建文件 */
    res = backend->create(pobj, false, header_data, header_size,
                         attr_data, attr_size, &obj->fh);
    if (res != TEE_SUCCESS) {
        free(header_data);
        if (attr_data) free(attr_data);
        gp_obj_free(obj);
        gp_pobj_release(pobj);
        return res;
    }

    /* 写入初始数据 */
    if (initialData && initialDataLen > 0) {
        size_t data_offset = header_size + attr_size;
        res = backend->write(obj->fh, data_offset, initialData, initialDataLen);
        if (res != TEE_SUCCESS) {
            backend->close(&obj->fh);
            backend->remove(pobj);
            free(header_data);
            if (attr_data) free(attr_data);
            gp_obj_free(obj);
            gp_pobj_release(pobj);
            return res;
        }
        obj->info.dataSize = initialDataLen;
    }

    /* 完成对象创建 */
    obj->pobj = pobj;
    obj->info.handleFlags = TEE_HANDLE_FLAG_PERSISTENT;
    obj->info.dataPosition = 0;
    obj->ds_pos = header_size + attr_size;

    gp_pobj_create_final(pobj);
    gp_obj_add(obj);

    free(header_data);
    if (attr_data) free(attr_data);

    *object = (TEE_ObjectHandle)obj->handle_id;
    return TEE_SUCCESS;
}
```

### 6.3 Data Stream Access Functions实现

```c
/* TEE_ReadObjectData - 读取对象数据 */
TEE_Result TEE_ReadObjectData(TEE_ObjectHandle object, void *buffer,
                             uint32_t size, uint32_t *count) {
    struct gp_tee_obj *obj;
    TEE_Result res;
    size_t bytes_read;

    if (!buffer || !count)
        return TEE_ERROR_BAD_PARAMETERS;

    res = gp_obj_get(object, &obj);
    if (res != TEE_SUCCESS)
        return res;

    if (obj->busy)
        return TEE_ERROR_BUSY;

    /* 检查读权限 */
    if (!(obj->info.objectUsage & TEE_USAGE_EXTRACTABLE))
        return TEE_ERROR_ACCESS_DENIED;

    *count = 0;

    /* 瞬态对象没有数据流 */
    if (!obj->pobj)
        return TEE_SUCCESS;

    /* 从文件读取数据 */
    bytes_read = size;
    res = obj->pobj->backend->read(obj->fh, obj->ds_pos, buffer, &bytes_read);
    if (res != TEE_SUCCESS)
        return res;

    *count = (uint32_t)bytes_read;
    obj->ds_pos += bytes_read;
    obj->info.dataPosition = obj->ds_pos - gp_get_data_offset(obj);

    return TEE_SUCCESS;
}

/* TEE_WriteObjectData - 写入对象数据 */
TEE_Result TEE_WriteObjectData(TEE_ObjectHandle object, const void *buffer, uint32_t size) {
    struct gp_tee_obj *obj;
    TEE_Result res;

    if (!buffer && size > 0)
        return TEE_ERROR_BAD_PARAMETERS;

    res = gp_obj_get(object, &obj);
    if (res != TEE_SUCCESS)
        return res;

    if (obj->busy)
        return TEE_ERROR_BUSY;

    /* 检查写权限 */
    if (!(obj->info.handleFlags & TEE_HANDLE_FLAG_PERSISTENT))
        return TEE_ERROR_ACCESS_DENIED;

    /* 瞬态对象不支持数据流写入 */
    if (!obj->pobj)
        return TEE_ERROR_ACCESS_DENIED;

    /* 写入文件 */
    res = obj->pobj->backend->write(obj->fh, obj->ds_pos, buffer, size);
    if (res != TEE_SUCCESS)
        return res;

    obj->ds_pos += size;
    obj->info.dataPosition = obj->ds_pos - gp_get_data_offset(obj);

    /* 更新数据大小 */
    if (obj->info.dataPosition > obj->info.dataSize) {
        obj->info.dataSize = obj->info.dataPosition;
        res = gp_update_object_header(obj);
        if (res != TEE_SUCCESS)
            return res;
    }

    return TEE_SUCCESS;
}
```

## 7. 系统集成和性能优化

### 7.1 存储后端管理器

```c
/* 全局存储后端管理器 */
struct gp_storage_manager {
    struct gp_storage_backend backends[3];  /* 三种存储类型的后端 */
    bool initialized;
    mutex_t manager_lock;
};

static struct gp_storage_manager g_storage_manager = {
    .initialized = false,
    .manager_lock = MUTEX_INITIAL_VALUE(g_storage_manager.manager_lock),
};

/* 获取存储后端实例 */
struct gp_storage_backend *gp_get_storage_backend(uint32_t storage_type) {
    struct gp_storage_backend *backend;
    int backend_idx;
    TEE_Result res;

    /* 确定后端索引 */
    switch (storage_type) {
        case TEE_STORAGE_PRIVATE:
            backend_idx = 0;
            break;
        case TEE_STORAGE_PERSO:
            backend_idx = 1;
            break;
        case TEE_STORAGE_PROTECTED:
            backend_idx = 2;
            break;
        default:
            return NULL;
    }

    mutex_acquire(&g_storage_manager.manager_lock);

    /* 延迟初始化 */
    if (!g_storage_manager.initialized) {
        memset(g_storage_manager.backends, 0, sizeof(g_storage_manager.backends));
        g_storage_manager.initialized = true;
    }

    backend = &g_storage_manager.backends[backend_idx];

    /* 如果后端未初始化，则初始化它 */
    if (!backend->session_active) {
        res = gp_storage_backend_init(backend, storage_type);
        if (res != TEE_SUCCESS) {
            mutex_release(&g_storage_manager.manager_lock);
            return NULL;
        }
    }

    mutex_release(&g_storage_manager.manager_lock);
    return backend;
}
```

### 7.2 性能优化特性

1. **会话复用**：每种存储类型维护单一会话，避免重复创建
2. **路径缓存**：缓存常用路径，减少字符串操作开销
3. **批量操作**：支持批量读写操作，提高I/O效率
4. **异步I/O**：支持异步文件操作，提高并发性能

### 7.3 错误恢复和事务支持

1. **原子操作**：文件创建、重命名等操作的原子性保证
2. **错误回滚**：操作失败时的自动回滚机制
3. **资源清理**：异常情况下的资源自动清理
4. **一致性检查**：文件系统一致性验证和修复

## 8. 总结

本设计文档提供了一套完整的GP存储API架构，具有以下核心特性：

1. **OP-TEE架构完全适配**：完整复制OP-TEE的双层对象模型和管理方法
2. **Trusty存储服务深度集成**：gp_storage_backend完美适配Trusty存储API
3. **GP标准严格遵循**：26个GP存储API完全符合标准规范
4. **高性能设计**：会话复用、路径缓存、批量操作等优化特性
5. **可靠性保证**：完整的错误处理、事务支持和资源管理

该架构为Trusty TEE提供了一套成熟、可靠、高性能的GP存储功能实现基础。
```
```
