# Trusty TEE GP存储方案接口设计文档

## 3.5 接口设计

### 3.5.1 用户接口

#### 3.5.1.1 GP存储模块

接口清单：

| 序号 | 接口名称 | 描述 |
|------|----------|------|
| 1 | TEE_OpenPersistentObject | 打开持久对象 |
| 2 | TEE_CreatePersistentObject | 创建持久对象 |
| 3 | TEE_CloseAndDeletePersistentObject | 关闭并删除持久对象 |
| 4 | TEE_RenamePersistentObject | 重命名持久对象 |
| 5 | TEE_AllocatePersistentObjectEnumerator | 分配持久对象枚举器 |
| 6 | TEE_FreePersistentObjectEnumerator | 释放持久对象枚举器 |
| 7 | TEE_StartPersistentObjectEnumerator | 启动持久对象枚举器 |
| 8 | TEE_ResetPersistentObjectEnumerator | 重置持久对象枚举器 |
| 9 | TEE_GetNextPersistentObject | 获取下一个持久对象 |
| 10 | TEE_ReadObjectData | 读取对象数据 |
| 11 | TEE_WriteObjectData | 写入对象数据 |
| 12 | TEE_TruncateObjectData | 截断对象数据 |
| 13 | TEE_SeekObjectData | 定位对象数据 |
| 14 | TEE_GetObjectInfo1 | 获取对象信息 |
| 15 | TEE_RestrictObjectUsage1 | 限制对象使用权限 |
| 16 | TEE_GetObjectBufferAttribute | 获取对象缓冲区属性 |
| 17 | TEE_GetObjectValueAttribute | 获取对象值属性 |
| 18 | TEE_CloseObject | 关闭对象 |
| 19 | TEE_AllocateTransientObject | 分配瞬态对象 |
| 20 | TEE_FreeTransientObject | 释放瞬态对象 |
| 21 | TEE_ResetTransientObject | 重置瞬态对象 |
| 22 | TEE_PopulateTransientObject | 填充瞬态对象 |
| 23 | TEE_InitRefAttribute | 初始化引用属性 |
| 24 | TEE_InitValueAttribute | 初始化值属性 |
| 25 | TEE_CopyObjectAttributes1 | 复制对象属性 |

##### 3.5.1.1.1 接口说明

**TEE_OpenPersistentObject用户接口**

| 项目 | 内容 |
|------|------|
| 函数接口 | `TEE_Result TEE_OpenPersistentObject(uint32_t storageID, const void *objectID, uint32_t objectIDLen, uint32_t flags, TEE_ObjectHandle *object)` |
| 输入 | storageID: 存储类型ID（TEE_STORAGE_PRIVATE/PERSO/PROTECTED）<br/>objectID: 对象标识符指针<br/>objectIDLen: 对象标识符长度<br/>flags: 访问标志位 |
| 输出 | object: 输出对象句柄指针 |
| 返回值 | TEE_SUCCESS<br/>TEE_ERROR_BAD_PARAMETERS<br/>TEE_ERROR_ITEM_NOT_FOUND<br/>TEE_ERROR_ACCESS_DENIED<br/>TEE_ERROR_OUT_OF_MEMORY |
| 说明 | 打开指定存储类型中的持久对象。支持三种GP标准存储类型，提供完整的访问权限控制。 |

**TEE_CreatePersistentObject用户接口**

| 项目 | 内容 |
|------|------|
| 函数接口 | `TEE_Result TEE_CreatePersistentObject(uint32_t storageID, const void *objectID, uint32_t objectIDLen, uint32_t flags, TEE_ObjectHandle attributes, const void *initialData, uint32_t initialDataLen, TEE_ObjectHandle *object)` |
| 输入 | storageID: 存储类型ID<br/>objectID: 对象标识符指针<br/>objectIDLen: 对象标识符长度<br/>flags: 创建标志位<br/>attributes: 属性对象句柄<br/>initialData: 初始数据指针<br/>initialDataLen: 初始数据长度 |
| 输出 | object: 输出对象句柄指针 |
| 返回值 | TEE_SUCCESS<br/>TEE_ERROR_BAD_PARAMETERS<br/>TEE_ERROR_ACCESS_CONFLICT<br/>TEE_ERROR_STORAGE_NOT_AVAILABLE<br/>TEE_ERROR_OUT_OF_MEMORY |
| 说明 | 创建新的持久对象。支持属性继承和初始数据写入，确保原子性创建操作。 |

**TEE_ReadObjectData用户接口**

| 项目 | 内容 |
|------|------|
| 函数接口 | `TEE_Result TEE_ReadObjectData(TEE_ObjectHandle object, void *buffer, uint32_t size, uint32_t *count)` |
| 输入 | object: 对象句柄<br/>buffer: 数据缓冲区指针<br/>size: 要读取的字节数 |
| 输出 | buffer: 填充读取的数据<br/>count: 实际读取的字节数 |
| 返回值 | TEE_SUCCESS<br/>TEE_ERROR_BAD_PARAMETERS<br/>TEE_ERROR_ACCESS_DENIED<br/>TEE_ERROR_BUSY |
| 说明 | 从对象数据流读取数据。支持流式读取，自动更新数据流位置。 |

**TEE_WriteObjectData用户接口**

| 项目 | 内容 |
|------|------|
| 函数接口 | `TEE_Result TEE_WriteObjectData(TEE_ObjectHandle object, const void *buffer, uint32_t size)` |
| 输入 | object: 对象句柄<br/>buffer: 数据缓冲区指针<br/>size: 要写入的字节数 |
| 输出 | 无 |
| 返回值 | TEE_SUCCESS<br/>TEE_ERROR_BAD_PARAMETERS<br/>TEE_ERROR_ACCESS_DENIED<br/>TEE_ERROR_STORAGE_NOT_AVAILABLE |
| 说明 | 向对象数据流写入数据。支持流式写入，自动扩展对象大小。 |

**TEE_GetObjectInfo1用户接口**

| 项目 | 内容 |
|------|------|
| 函数接口 | `void TEE_GetObjectInfo1(TEE_ObjectHandle object, TEE_ObjectInfo *objectInfo)` |
| 输入 | object: 对象句柄<br/>objectInfo: 对象信息结构指针 |
| 输出 | objectInfo: 填充的对象信息结构 |
| 返回值 | 无 (void)。若参数无效则行为是 panic。 |
| 说明 | 获取对象的详细信息，包括类型、大小、使用权限等元数据。 |

**TEE_CloseAndDeletePersistentObject用户接口**

| 项目 | 内容 |
|------|------|
| 函数接口 | `TEE_Result TEE_CloseAndDeletePersistentObject(TEE_ObjectHandle object)` |
| 输入 | object: 要删除的对象句柄 |
| 输出 | 无 |
| 返回值 | TEE_SUCCESS<br/>TEE_ERROR_BAD_PARAMETERS<br/>TEE_ERROR_ACCESS_DENIED<br/>TEE_ERROR_STORAGE_NOT_AVAILABLE |
| 说明 | 关闭对象句柄并从存储中永久删除对象。操作不可逆转。 |

**TEE_GetObjectBufferAttribute用户接口**

| 项目 | 内容 |
|------|------|
| 函数接口 | `TEE_Result TEE_GetObjectBufferAttribute(TEE_ObjectHandle object, uint32_t attributeID, void *buffer, uint32_t *size)` |
| 输入 | object: 对象句柄<br/>attributeID: 属性标识符<br/>buffer: 属性数据缓冲区<br/>size: 缓冲区大小指针 |
| 输出 | buffer: 填充的属性数据<br/>size: 实际属性数据大小 |
| 返回值 | TEE_SUCCESS<br/>TEE_ERROR_BAD_PARAMETERS<br/>TEE_ERROR_ITEM_NOT_FOUND<br/>TEE_ERROR_SHORT_BUFFER |
| 说明 | 获取对象的缓冲区类型属性。支持密钥材料、证书等二进制属性数据的获取。 |

**TEE_GetObjectValueAttribute用户接口**

| 项目 | 内容 |
|------|------|
| 函数接口 | `TEE_Result TEE_GetObjectValueAttribute(TEE_ObjectHandle object, uint32_t attributeID, uint32_t *a, uint32_t *b)` |
| 输入 | object: 对象句柄<br/>attributeID: 属性标识符 |
| 输出 | a: 属性值A<br/>b: 属性值B |
| 返回值 | TEE_SUCCESS<br/>TEE_ERROR_BAD_PARAMETERS<br/>TEE_ERROR_ITEM_NOT_FOUND |
| 说明 | 获取对象的值类型属性。支持密钥长度、算法类型等数值属性的获取。 |

**TEE_AllocateTransientObject用户接口**

| 项目 | 内容 |
|------|------|
| 函数接口 | `TEE_Result TEE_AllocateTransientObject(uint32_t objectType, uint32_t maxObjectSize, TEE_ObjectHandle *object)` |
| 输入 | objectType: 对象类型<br/>maxObjectSize: 最大对象大小 |
| 输出 | object: 输出对象句柄指针 |
| 返回值 | TEE_SUCCESS<br/>TEE_ERROR_BAD_PARAMETERS<br/>TEE_ERROR_OUT_OF_MEMORY<br/>TEE_ERROR_NOT_SUPPORTED |
| 说明 | 分配新的瞬态对象。支持密钥对象、数据对象等多种类型的瞬态对象创建。 |

**TEE_FreeTransientObject用户接口**

| 项目 | 内容 |
|------|------|
| 函数接口 | `void TEE_FreeTransientObject(TEE_ObjectHandle object)` |
| 输入 | object: 要释放的瞬态对象句柄 |
| 输出 | 无 |
| 返回值 | 无 (void)。若句柄无效则行为是 panic。 |
| 说明 | 释放瞬态对象及其关联的所有资源。清除敏感数据，释放内存。 |

**TEE_ResetTransientObject用户接口**

| 项目 | 内容 |
|------|------|
| 函数接口 | `void TEE_ResetTransientObject(TEE_ObjectHandle object)` |
| 输入 | object: 要重置的瞬态对象句柄 |
| 输出 | 无 |
| 返回值 | 无 (void)。若句柄无效则行为是 panic。 |
| 说明 | 重置瞬态对象到初始状态。清除所有属性和数据，保持对象句柄有效。 |

**TEE_PopulateTransientObject用户接口**

| 项目 | 内容 |
|------|------|
| 函数接口 | `TEE_Result TEE_PopulateTransientObject(TEE_ObjectHandle object, const TEE_Attribute *attrs, uint32_t attrCount)` |
| 输入 | object: 瞬态对象句柄<br/>attrs: 属性数组指针<br/>attrCount: 属性数量 |
| 输出 | 无 |
| 返回值 | TEE_SUCCESS<br/>TEE_ERROR_BAD_PARAMETERS<br/>TEE_ERROR_ACCESS_DENIED |
| 说明 | 使用提供的属性填充瞬态对象。支持密钥材料、算法参数等属性的批量设置。 |

**TEE_InitRefAttribute用户接口**

| 项目 | 内容 |
|------|------|
| 函数接口 | `void TEE_InitRefAttribute(TEE_Attribute *attr, uint32_t attributeID, const void *buffer, uint32_t length)` |
| 输入 | attr: 属性结构指针<br/>attributeID: 属性标识符<br/>buffer: 属性数据缓冲区<br/>length: 数据长度 |
| 输出 | attr: 初始化的属性结构 |
| 返回值 | 无 (void)。若参数无效则行为是 panic。 |
| 说明 | 初始化引用类型属性。用于设置指向外部缓冲区的属性数据。 |

**TEE_InitValueAttribute用户接口**

| 项目 | 内容 |
|------|------|
| 函数接口 | `void TEE_InitValueAttribute(TEE_Attribute *attr, uint32_t attributeID, uint32_t a, uint32_t b)` |
| 输入 | attr: 属性结构指针<br/>attributeID: 属性标识符<br/>a: 属性值A<br/>b: 属性值B |
| 输出 | attr: 初始化的属性结构 |
| 返回值 | 无 (void)。若参数无效则行为是 panic。 |
| 说明 | 初始化值类型属性。用于设置数值型属性，如密钥长度、算法类型等。 |

**TEE_CopyObjectAttributes1用户接口**

| 项目 | 内容 |
|------|------|
| 函数接口 | `TEE_Result TEE_CopyObjectAttributes1(TEE_ObjectHandle destObject, TEE_ObjectHandle srcObject)` |
| 输入 | destObject: 目标对象句柄<br/>srcObject: 源对象句柄 |
| 输出 | 无 |
| 返回值 | TEE_SUCCESS<br/>TEE_ERROR_BAD_PARAMETERS<br/>TEE_ERROR_ACCESS_DENIED<br/>TEE_ERROR_CORRUPT_OBJECT |
| 说明 | 将源对象的属性复制到目标对象。支持完整的属性克隆，包括密钥材料和元数据。 |

### 3.5.2 外部接口

#### 3.5.2.1 GP存储模块

接口清单：

| 序号 | 接口名称 | 描述 |
|------|----------|------|
| 1 | storage_open_session | 打开存储会话接口 |
| 2 | storage_close_session | 关闭存储会话接口 |
| 3 | storage_open_file | 打开文件接口 |
| 4 | storage_close_file | 关闭文件接口 |
| 5 | storage_read | 读取文件数据接口 |
| 6 | storage_write | 写入文件数据接口 |
| 7 | storage_delete_file | 删除文件接口 |
| 8 | storage_move_file | 移动文件接口 |

##### 3.5.2.1.1 接口说明

**storage_open_session外部接口**

| 项目 | 内容 |
|------|------|
| 函数接口 | `int storage_open_session(storage_session_t *session, const char *port)` |
| 输入 | session: 存储会话结构指针<br/>port: 存储服务端口名称 |
| 输出 | session: 初始化的存储会话 |
| 返回值 | 成功返回NO_ERROR，失败返回负错误码 |
| 说明 | 现有Trusty接口，无需修改。GP存储通过端口名称映射到不同存储类型：private/perso/protected。 |

**storage_open_file外部接口**

| 项目 | 内容 |
|------|------|
| 函数接口 | `int storage_open_file(storage_session_t session, file_handle_t *handle, const char *name, uint32_t flags, uint32_t opflags)` |
| 输入 | session: 存储会话<br/>name: 文件路径名称<br/>flags: 打开标志<br/>opflags: 操作标志 |
| 输出 | handle: 文件句柄指针 |
| 返回值 | 成功返回NO_ERROR，失败返回负错误码 |
| 说明 | 现有Trusty接口，无需修改。GP存储直接复用此接口进行文件操作。 |

**storage_read外部接口**

| 项目 | 内容 |
|------|------|
| 函数接口 | `int storage_read(file_handle_t handle, storage_off_t pos, void *buf, size_t size)` |
| 输入 | handle: 文件句柄<br/>pos: 读取位置偏移<br/>buf: 数据缓冲区<br/>size: 读取大小 |
| 输出 | buf: 填充读取的数据 |
| 返回值 | 成功返回读取字节数，失败返回负错误码 |
| 说明 | 现有Trusty接口，无需修改。GP存储通过此接口实现对象数据流读取。 |

**storage_write外部接口**

| 项目 | 内容 |
|------|------|
| 函数接口 | `int storage_write(file_handle_t handle, storage_off_t pos, const void *buf, size_t size, uint32_t opflags)` |
| 输入 | handle: 文件句柄<br/>pos: 写入位置偏移<br/>buf: 数据缓冲区<br/>size: 写入大小<br/>opflags: 操作标志 |
| 输出 | 无 |
| 返回值 | 成功返回写入字节数，失败返回负错误码 |
| 说明 | 现有Trusty接口，无需修改。GP存储通过此接口实现对象数据流写入。 |

### 3.5.3 内部接口

#### 3.5.3.1 GP存储模块

接口清单：

| 序号 | 接口名称 | 描述 |
|------|----------|------|
| 1 | rctee_gp_obj_alloc | 分配TEE对象 |
| 2 | rctee_gp_obj_free | 释放TEE对象 |
| 3 | rctee_gp_obj_get | 获取TEE对象 |
| 4 | rctee_gp_obj_close | 关闭TEE对象 |
| 5 | rctee_gp_pobj_get | 获取持久对象 |
| 6 | rctee_gp_pobj_release | 释放持久对象 |
| 7 | rctee_gp_storage_backend_init | 初始化存储后端 |
| 8 | rctee_gp_get_storage_backend | 获取存储后端 |
| 9 | rctee_gp_build_object_path | 构建对象路径 |
| 10 | rctee_gp_convert_storage_error | 转换存储错误码 |

##### 3.5.3.1.1 接口说明

**rctee_gp_obj_alloc内部接口**

| 项目 | 内容 |
|------|------|
| 函数接口 | `struct rctee_gp_obj *rctee_gp_obj_alloc(void)` |
| 输入 | 无 |
| 输出 | 返回分配的TEE对象指针 |
| 返回值 | 成功返回对象指针，失败返回NULL |
| 说明 | 分配并初始化新的TEE对象结构。设置默认值，初始化互斥锁，分配唯一句柄ID。 |

**rctee_gp_obj_get内部接口**

| 项目 | 内容 |
|------|------|
| 函数接口 | `TEE_Result rctee_gp_obj_get(TEE_ObjectHandle object, struct rctee_gp_obj **obj)` |
| 输入 | object: 对象句柄<br/>obj: 输出对象指针的指针 |
| 输出 | obj: 返回对象指针 |
| 返回值 | TEE_SUCCESS: 成功获取对象<br/>TEE_ERROR_BAD_PARAMETERS: 句柄无效<br/>TEE_ERROR_BAD_STATE: 对象状态异常 |
| 说明 | 根据句柄获取TEE对象。验证句柄有效性，检查对象状态，返回对象指针。 |

**rctee_gp_pobj_get内部接口**

| 项目 | 内容 |
|------|------|
| 函数接口 | `TEE_Result rctee_gp_pobj_get(struct uuid *uuid, void *obj_id, uint32_t obj_id_len, uint32_t flags, enum rctee_gp_pobj_usage usage, struct rctee_gp_storage_backend *backend, struct rctee_gp_pobj **obj)` |
| 输入 | uuid: TA UUID指针<br/>obj_id: 对象ID指针<br/>obj_id_len: 对象ID长度<br/>flags: 访问标志<br/>usage: 使用类型<br/>backend: 存储后端指针 |
| 输出 | obj: 输出持久对象指针 |
| 返回值 | TEE_SUCCESS: 成功获取对象<br/>TEE_ERROR_BAD_PARAMETERS: 参数无效<br/>TEE_ERROR_OUT_OF_MEMORY: 内存不足<br/>TEE_ERROR_ACCESS_DENIED: 访问被拒绝 |
| 说明 | 获取或创建持久对象。管理引用计数，构建存储路径，处理TA隔离。 |

**rctee_gp_storage_backend_init内部接口**

| 项目 | 内容 |
|------|------|
| 函数接口 | `TEE_Result rctee_gp_storage_backend_init(struct rctee_gp_storage_backend *backend, uint32_t storage_type)` |
| 输入 | backend: 存储后端结构指针<br/>storage_type: 存储类型 |
| 输出 | backend: 初始化的存储后端 |
| 返回值 | TEE_SUCCESS: 成功初始化<br/>TEE_ERROR_BAD_PARAMETERS: 参数无效<br/>TEE_ERROR_GENERIC: 初始化失败 |
| 说明 | 初始化存储后端。打开Trusty存储会话，设置函数指针，配置存储类型映射。 |

**rctee_gp_build_object_path内部接口**

| 项目 | 内容 |
|------|------|
| 函数接口 | `int rctee_gp_build_object_path(const struct uuid *ta_uuid, const void *obj_id, uint32_t obj_id_len, uint32_t storage_type, char *path, size_t path_size)` |
| 输入 | ta_uuid: TA UUID指针<br/>obj_id: 对象ID指针<br/>obj_id_len: 对象ID长度<br/>storage_type: 存储类型<br/>path_size: 路径缓冲区大小 |
| 输出 | path: 构建的完整对象路径 |
| 返回值 | NO_ERROR: 成功构建路径<br/>-EINVAL: 参数无效<br/>-ENAMETOOLONG: 路径过长 |
| 说明 | 构建完整的对象存储路径。格式：storage_type/ta_uuid/obj_id_hex。确保TA间存储隔离。 |

**rctee_gp_convert_storage_error内部接口**

| 项目 | 内容 |
|------|------|
| 函数接口 | `TEE_Result rctee_gp_convert_storage_error(int storage_err)` |
| 输入 | storage_err: Trusty存储错误码 |
| 输出 | 无 |
| 返回值 | 对应的GP TEE错误码 |
| 说明 | 将Trusty存储服务错误码转换为GP标准错误码。提供完整的错误码映射表。 |

#### 3.5.3.2 存储后端管理模块

接口清单：

| 序号 | 接口名称 | 描述 |
|------|----------|------|
| 1 | rctee_gp_storage_manager_init | 初始化存储管理器 |
| 2 | rctee_gp_get_storage_backend | 获取存储后端实例 |
| 3 | rctee_gp_storage_open_impl | 存储打开实现 |
| 4 | rctee_gp_storage_create_impl | 存储创建实现 |
| 5 | rctee_gp_storage_remove_impl | 存储删除实现 |
| 6 | rctee_gp_load_object_header | 加载对象头部 |
| 7 | rctee_gp_create_object_header | 创建对象头部 |
| 8 | rctee_gp_serialize_object_attributes | 序列化对象属性 |

##### 3.5.3.2.1 接口说明

**rctee_gp_get_storage_backend内部接口**

| 项目 | 内容 |
|------|------|
| 函数接口 | `struct rctee_gp_storage_backend *rctee_gp_get_storage_backend(uint32_t storage_id)` |
| 输入 | storage_id: 存储类型ID（TEE_STORAGE_PRIVATE/PERSO/PROTECTED） |
| 输出 | 返回对应的存储后端指针 |
| 返回值 | 成功返回后端指针，失败返回NULL |
| 说明 | 根据存储类型ID获取对应的存储后端实例。支持三种GP标准存储类型的后端管理。 |

**rctee_gp_storage_open_impl内部接口**

| 项目 | 内容 |
|------|------|
| 函数接口 | `static TEE_Result rctee_gp_storage_open_impl(struct rctee_gp_pobj *pobj, size_t *size, file_handle_t *fh)` |
| 输入 | pobj: 持久对象指针<br/>size: 文件大小指针 |
| 输出 | size: 返回文件大小<br/>fh: 返回文件句柄 |
| 返回值 | TEE_SUCCESS: 成功打开文件<br/>TEE_ERROR_ITEM_NOT_FOUND: 文件不存在<br/>TEE_ERROR_ACCESS_DENIED: 访问被拒绝 |
| 说明 | 适配Trusty storage_open_file API。打开持久对象对应的文件，获取文件大小。 |

**rctee_gp_load_object_header内部接口**

| 项目 | 内容 |
|------|------|
| 函数接口 | `TEE_Result rctee_gp_load_object_header(struct rctee_gp_obj *obj)` |
| 输入 | obj: TEE对象指针 |
| 输出 | obj: 填充对象信息的TEE对象 |
| 返回值 | TEE_SUCCESS: 成功加载头部<br/>TEE_ERROR_CORRUPT_OBJECT: 对象头部损坏<br/>TEE_ERROR_GENERIC: 读取失败 |
| 说明 | 从文件加载对象头部信息。解析对象类型、大小、属性等元数据，验证完整性。 |

## 3.6 数据结构设计

### 3.6.1 逻辑结构设计要点

#### 3.6.1.1 GP存储模块

```c
/* GP TEE对象结构 - 基于OP-TEE tee_obj设计 */
struct rctee_gp_obj {
    struct list_node link;              /* TA私有对象链表节点 */
    TEE_ObjectInfo info;                /* GP标准对象信息 */
    bool busy;                          /* 操作忙标志 */
    uint32_t have_attrs;                /* 属性位字段 */
    void *attr;                         /* 属性数据指针 */
    size_t ds_pos;                      /* 数据流位置偏移 */
    struct rctee_gp_pobj *pobj;         /* 持久化对象指针 */
    file_handle_t fh;                   /* Trusty文件句柄 */
    uint32_t handle_id;                 /* 句柄唯一ID */
    mutex_t obj_lock;                   /* 对象锁 */
};

/* GP持久对象结构 - 基于OP-TEE tee_pobj设计 */
struct rctee_gp_pobj {
    struct list_node link;              /* 全局持久对象链表节点 */
    uint32_t refcnt;                    /* 引用计数 */
    struct uuid uuid;                   /* TA UUID */
    void *obj_id;                       /* 对象ID */
    uint32_t obj_id_len;                /* 对象ID长度 */
    uint32_t flags;                     /* 访问标志 */
    uint32_t obj_info_usage;            /* 使用权限 */
    bool temporary;                     /* 创建过程中可修改 */
    bool creating;                      /* 正在创建状态 */
    struct rctee_gp_storage_backend *backend;  /* 存储后端接口 */
    char storage_path[GP_MAX_PATH_LEN]; /* 存储路径 */
    mutex_t pobj_lock;                  /* 持久对象锁 */
};

/* GP存储后端接口结构 */
struct rctee_gp_storage_backend {
    storage_session_t session;          /* Trusty存储会话 */
    const char *port_name;              /* 存储服务端口名 */
    bool session_active;                /* 会话状态标志 */
    uint32_t storage_type;              /* 存储类型 */

    /* 文件操作函数指针 */
    TEE_Result (*open)(struct rctee_gp_pobj *pobj, size_t *size, file_handle_t *fh);
    TEE_Result (*create)(struct rctee_gp_pobj *pobj, bool overwrite,
                        const void *head, size_t head_size,
                        const void *attr, size_t attr_size,
                        file_handle_t *fh);
    TEE_Result (*close)(file_handle_t *fh);
    TEE_Result (*read)(file_handle_t fh, size_t pos, void *buf, size_t *len);
    TEE_Result (*write)(file_handle_t fh, size_t pos, const void *buf, size_t len);
    TEE_Result (*remove)(struct rctee_gp_pobj *pobj);

    mutex_t backend_lock;               /* 后端锁 */
};

/* 持久对象使用类型枚举 */
enum rctee_gp_pobj_usage {
    RCTEE_GP_POBJ_USAGE_OPEN,          /* 打开操作 */
    RCTEE_GP_POBJ_USAGE_RENAME,        /* 重命名操作 */
    RCTEE_GP_POBJ_USAGE_CREATE,        /* 创建操作 */
    RCTEE_GP_POBJ_USAGE_ENUM,          /* 枚举操作 */
};

/* 存储管理器结构 */
struct rctee_gp_storage_manager {
    struct rctee_gp_storage_backend backends[3];  /* 三种存储类型后端 */
    bool initialized;                              /* 初始化状态标志 */
    mutex_t manager_lock;                          /* 管理器锁 */
};

/* 目录枚举状态结构 */
struct rctee_gp_dir_enum_state {
    storage_session_t session;                     /* 存储会话 */
    struct storage_open_dir_state state;           /* Trusty目录状态 */
    char prefix[GP_MAX_PATH_LEN];                  /* 路径前缀 */
    uint32_t storage_type;                         /* 存储类型 */
    struct uuid ta_uuid;                           /* TA UUID */
    bool active;                                   /* 枚举活跃状态 */
};

/* 对象头部结构 */
struct rctee_gp_object_header {
    uint32_t magic;                                /* 魔数标识 */
    uint32_t version;                              /* 版本号 */
    uint32_t obj_type;                             /* 对象类型 */
    uint32_t obj_size;                             /* 对象大小 */
    uint32_t attr_count;                           /* 属性数量 */
    uint32_t attr_size;                            /* 属性总大小 */
    uint32_t data_size;                            /* 数据大小 */
    uint32_t checksum;                             /* 校验和 */
};

/* 存储类型常量定义 */
#define GP_MAX_PATH_LEN                 256        /* 最大路径长度 */
#define GP_MAX_FILENAME_LEN             128        /* 最大文件名长度 */
#define GP_MAX_OBJ_ID_LEN               64         /* 最大对象ID长度 */
#define GP_OBJECT_HEADER_MAGIC          0x47504F42 /* 'GPOB' */
#define GP_OBJECT_HEADER_VERSION        1          /* 头部版本 */

/* 存储端口名称映射 */
#define GP_STORAGE_PRIVATE_PORT         "com.android.trusty.storage.proxy"
#define GP_STORAGE_PERSO_PORT           "com.android.trusty.storage.proxy.perso"
#define GP_STORAGE_PROTECTED_PORT       "com.android.trusty.storage.proxy.protected"
```

#### 3.6.1.2 错误码映射表

| Trusty存储错误码 | GP TEE错误码 | 描述 |
|------------------|--------------|------|
| NO_ERROR | TEE_SUCCESS | 操作成功 |
| ERR_NOT_FOUND | TEE_ERROR_ITEM_NOT_FOUND | 对象不存在 |
| ERR_ACCESS_DENIED | TEE_ERROR_ACCESS_DENIED | 访问被拒绝 |
| ERR_INVALID_ARGS | TEE_ERROR_BAD_PARAMETERS | 参数无效 |
| ERR_NO_MEMORY | TEE_ERROR_OUT_OF_MEMORY | 内存不足 |
| ERR_NOT_ENOUGH_BUFFER | TEE_ERROR_SHORT_BUFFER | 缓冲区不足 |
| ERR_ALREADY_EXISTS | TEE_ERROR_ACCESS_CONFLICT | 对象已存在 |
| ERR_NOT_READY | TEE_ERROR_STORAGE_NOT_AVAILABLE | 存储不可用 |
| ERR_IO | TEE_ERROR_GENERIC | 通用I/O错误 |
| ERR_NOT_SUPPORTED | TEE_ERROR_NOT_SUPPORTED | 操作不支持 |

#### 3.6.1.3 存储路径规范

**路径格式规范：**
- **私有存储**: `private/{ta_uuid}/{obj_id_hex}`
- **个人存储**: `perso/{ta_uuid}/{obj_id_hex}`
- **保护存储**: `protected/{ta_uuid}/{obj_id_hex}`

**路径构建规则：**
1. TA UUID转换为36字符的标准格式（包含连字符）
2. 对象ID转换为十六进制字符串（小写）
3. 确保路径长度不超过GP_MAX_PATH_LEN限制
4. 支持TA间的完全存储隔离

#### 3.6.1.4 并发控制机制

**对象级锁定：**
- 每个rctee_gp_obj包含独立的mutex_t obj_lock
- 每个rctee_gp_pobj包含独立的mutex_t pobj_lock
- 支持细粒度的并发控制

**引用计数管理：**
- rctee_gp_pobj使用refcnt字段管理生命周期
- 自动释放零引用计数的持久对象
- 防止对象在使用过程中被意外释放

**忙状态标志：**
- busy字段防止对象的并发操作
- 在长时间操作期间设置忙状态
- 确保数据一致性和操作原子性

## 3.7 接口设计总结

### 3.7.1 设计特点

1. **OP-TEE架构完全复制**: 数据结构和管理方法完全基于OP-TEE成熟实现
2. **Trusty存储深度集成**: 通过rctee_gp_storage_backend完美适配Trusty存储API
3. **GP标准严格遵循**: 25个GP存储API完全符合GP TEE Internal Core API v1.3.1规范
4. **高性能优化设计**: 会话复用、路径缓存、批量操作等性能优化特性

### 3.7.2 接口统计

| 接口类型 | 数量 | 说明 |
|----------|------|------|
| GP标准用户接口 | 25个 | 完整的GP存储API集合 |
| Trusty外部接口 | 8个 | 现有Trusty存储服务接口 |
| 对象管理内部接口 | 10个 | TEE对象和持久对象管理 |
| 存储后端内部接口 | 8个 | 存储适配和实现接口 |
| **总计** | **51个** | **完整的存储功能接口体系** |

#### 3.7.2.1 用户接口分类统计

| 功能分类 | 接口数量 | 主要接口 |
|----------|----------|----------|
| 通用对象函数 | 5个 | TEE_GetObjectInfo1, TEE_RestrictObjectUsage1, TEE_GetObjectBufferAttribute, TEE_GetObjectValueAttribute, TEE_CloseObject |
| 瞬态对象函数 | 7个 | TEE_AllocateTransientObject, TEE_FreeTransientObject, TEE_ResetTransientObject, TEE_PopulateTransientObject, TEE_InitRefAttribute, TEE_InitValueAttribute, TEE_CopyObjectAttributes1 |
| 持久对象函数 | 5个 | TEE_OpenPersistentObject, TEE_CreatePersistentObject, TEE_CloseAndDeletePersistentObject, TEE_RenamePersistentObject |
| 持久对象枚举函数 | 5个 | TEE_AllocatePersistentObjectEnumerator, TEE_FreePersistentObjectEnumerator, TEE_StartPersistentObjectEnumerator, TEE_ResetPersistentObjectEnumerator, TEE_GetNextPersistentObject |
| 数据流访问函数 | 4个 | TEE_ReadObjectData, TEE_WriteObjectData, TEE_TruncateObjectData, TEE_SeekObjectData |

### 3.7.3 架构优势

1. **成熟稳定**: 基于OP-TEE验证的架构设计
2. **完全兼容**: 100%符合GP TEE标准规范
3. **高度集成**: 与Trusty存储服务无缝对接
4. **性能优化**: 支持高并发和大数据量操作
5. **安全可靠**: 完整的错误处理和并发控制机制
